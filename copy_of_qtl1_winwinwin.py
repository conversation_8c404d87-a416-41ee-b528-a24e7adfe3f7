# -*- coding: utf-8 -*-
"""Copy of QTL1_WINWINWIN.ipynb

Automatically generated by <PERSON><PERSON>.

Original file is located at
    https://colab.research.google.com/drive/1i1dBD_pk6UXINsJXJJktaYP-4hTCC0qY

# **XÁC ĐỊNH BÀI TOÁN: "DỰ ĐOÁN NHÂN VIÊN NGHĨ VIỆC"**
# **THU THẬP DỮ LIỆU TRÊN KAGGLE: https://www.kaggle.com/datasets/pavansubhasht/ibm-hr-analytics-attrition-dataset**

## Đây là bài toán phân loại nhị phân với biến mục tiêu 'Nghỉ_việc' có 2 lớp:
* Lớp 0: Nhân viên **Không** nghỉ việc
* Lớp 1: Nhân viên **Có** nghỉ việc

# **BƯỚC 1: Tải dữ liệu**

## Tải và in  dữ liệu với các cột tiếng Việt
"""

import warnings
import joblib

import matplotlib.pyplot as plt
import pandas as pd
import seaborn as sns
from imblearn.over_sampling import SMOTE
from IPython.display import display, HTML
from sklearn.ensemble import (
    AdaBoostClassifier,
    ExtraTreesClassifier,
    GradientBoostingClassifier,
    RandomForestClassifier,
)
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import classification_report, confusion_matrix
from sklearn.model_selection import GridSearchCV, StratifiedKFold, train_test_split
from sklearn.naive_bayes import GaussianNB
from sklearn.neighbors import KNeighborsClassifier
from sklearn.neural_network import MLPClassifier
from sklearn.preprocessing import LabelEncoder, StandardScaler
from sklearn.svm import SVC
from sklearn.tree import DecisionTreeClassifier
warnings.filterwarnings("ignore")

# Bước 1: Tải dataset
df = pd.read_csv('/content/WA_Fn-UseC_-HR-Employee-Attrition.csv')

# In bảng dữ liệu gốc với tên cột tiếng Anh
print(" Dữ liệu gốc (cột tiếng Anh):")
display(HTML(df.head().to_html(index=False)))

# Đổi tên cột sang tiếng Việt
df.rename(columns={
    'Age': 'Tuổi',
    'Attrition': 'Nghỉ_Việc',
    'BusinessTravel': 'Đi_Công_Tác',
    'DailyRate': 'Lương_Ngày',
    'Department': 'Phòng_Ban',
    'DistanceFromHome': 'Khoảng_Cách_Tới_Nhà',
    'Education': 'Trình_Độ_Học_Vấn',
    'EducationField': 'Ngành_Học',
    'EmployeeCount': 'Tổng_Nhân_Viên',
    'EmployeeNumber': 'Mã_Nhân_Viên',
    'EnvironmentSatisfaction': 'Mức_Độ_Hài_Lòng_Môi_Trường',
    'Gender': 'Giới_Tính',
    'HourlyRate': 'Lương_Giờ',
    'JobInvolvement': 'Mức_Độ_Tham_Gia_Công_Việc',
    'JobLevel': 'Cấp_Bậc_Công_Việc',
    'JobRole': 'Chức_Vụ',
    'JobSatisfaction': 'Mức_Độ_Hài_Lòng_Công_Việc',
    'MaritalStatus': 'Tình_Trạng_Hôn_Nhân',
    'MonthlyIncome': 'Lương_Tháng',
    'MonthlyRate': 'Lương_Tháng_Theo_Tỷ_Lệ',
    'NumCompaniesWorked': 'Số_Công_Ty_Đã_Làm',
    'Over18': 'Trên_18_Tuổi',
    'OverTime': 'Làm_Thêm',
    'PercentSalaryHike': 'Phần_Trăm_Tăng_Lương',
    'PerformanceRating': 'Đánh_Giá_Hiệu_Suất',
    'RelationshipSatisfaction': 'Mức_Hài_Lòng_Mối_Quan_Hệ',
    'StandardHours': 'Giờ_Làm_Tiêu_Chuẩn',
    'StockOptionLevel': 'Cấp_Độ_Tùy_Chọn_Cổ_Phần',
    'TotalWorkingYears': 'Tổng_Số_Năm_Làm_Việc',
    'TrainingTimesLastYear': 'Số_Lần_Đào_Tạo_Năm_Trước',
    'WorkLifeBalance': 'Cân_Bằng_Cuộc_Sống_Công_Việc',
    'YearsAtCompany': 'Số_Năm_Làm_Tại_Công_Ty',
    'YearsInCurrentRole': 'Số_Năm_Làm_Vị_Trí_Hiện_Tại',
    'YearsSinceLastPromotion': 'Số_Năm_Từ_Lần_Thăng_Chức_Gần_Nhất',
    'YearsWithCurrManager': 'Số_Năm_Với_Quản_Lý_Hiện_Tại'
}, inplace=True)

#  In bảng dữ liệu sau khi đổi tên sang tiếng Việt
print(" Dữ liệu sau khi đổi tên cột sang tiếng Việt:")
display(HTML(df.head().to_html(index=False)))

first_row_to_test = df.iloc[0].copy() #Lấy dòng đầu của bộ dữ liệu để kiểm thử sau

"""# **BƯỚC 2: Tiền xử lý dữ liệu (Chỉnh sửa)**

## 2.1 Bỏ các cột không cần thiết
"""

df.drop(['Mã_Nhân_Viên', 'Trên_18_Tuổi', 'Giờ_Làm_Tiêu_Chuẩn', 'Tổng_Nhân_Viên'], axis=1, inplace=True)

# --- Thêm bước Feature Engineering ---
# Tỷ lệ lương tháng trên tổng số năm làm việc
# (Có thể cho thấy nhân viên có kinh nghiệm nhưng lương thấp)
df['Tỷ_Lệ_Lương_Kinh_Nghiệm'] = df['Lương_Tháng'] / (df['Tổng_Số_Năm_Làm_Việc'] + 1) # +1 để tránh chia cho 0

# Sự ổn định trong công việc
# (Số năm làm ở công ty so với số năm làm ở vị trí hiện tại)
df['Tỷ_Lệ_Ổn_Định'] = df['Số_Năm_Làm_Vị_Trí_Hiện_Tại'] / (df['Số_Năm_Làm_Tại_Công_Ty'] + 1)

# Nhân viên làm quá nhiều tại một công ty mà không được thăng chức?
df['Năm_Không_Thăng_Chức'] = df['Số_Năm_Làm_Tại_Công_Ty'] - df['Số_Năm_Từ_Lần_Thăng_Chức_Gần_Nhất']

# Mức độ hài lòng tổng thể (kết hợp các chỉ số hài lòng)
df['Hài_Lòng_Tổng_Thể'] = (df['Mức_Độ_Hài_Lòng_Môi_Trường'] +
                           df['Mức_Độ_Hài_Lòng_Công_Việc'] +
                           df['Mức_Hài_Lòng_Mối_Quan_Hệ']) / 3

print("Đã tạo thêm các đặc trưng mới.")
display(df[['Tỷ_Lệ_Lương_Kinh_Nghiệm', 'Tỷ_Lệ_Ổn_Định', 'Năm_Không_Thăng_Chức', 'Hài_Lòng_Tổng_Thể']].head())

"""## 2.2 Mã hóa các biến"""

# Mã hóa biến mục tiêu 'Nghỉ_Việc' (0: Không, 1: Có)
le_y = LabelEncoder()
df['Nghỉ_Việc'] = le_y.fit_transform(df['Nghỉ_Việc'])

# Sử dụng One-Hot Encoding cho các biến phân loại
print(" Áp dụng One-Hot Encoding...")
categorical_cols = df.select_dtypes(include=['object']).columns
df = pd.get_dummies(df, columns=categorical_cols, drop_first=True)
print(f"Kích thước dữ liệu sau One-Hot Encoding: {df.shape}")

"""## 2.3 Tách Feature (X) và Target (Y)"""

X = df.drop('Nghỉ_Việc', axis=1)
y = df['Nghỉ_Việc']

# Lưu lại các cột sau khi xử lý để dùng cho dự đoán sau này
processed_columns = X.columns.tolist()

"""## 2.4 Chia dữ liệu thành tập Train và Test (70/30)"""

X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42, stratify=y)
print(f"\nKích thước tập huấn luyện: {X_train.shape}")
print(f"Kích thước tập kiểm tra: {X_test.shape}")

# >> Lưu lại các cột sau khi xử lý để dùng cho dự đoán sau này
processed_columns = X.columns.tolist()

# Vẽ biểu đồ phân phối Nghỉ việc trong tập train
plt.figure(figsize=(10, 6))
sns.countplot(data=pd.DataFrame({'Nghỉ_Việc': y_train}), x='Nghỉ_Việc', palette='viridis')
plt.title('Phân phối Nghỉ việc trong tập huấn luyện')
plt.xlabel('Nghỉ việc (0: Không, 1: Có)')
plt.ylabel('Số lượng nhân viên')

# Thêm số lượng cụ thể trên mỗi cột
counts = y_train.value_counts()
for i, count in enumerate(counts):
    plt.text(i, count, str(count), ha='center', va='bottom')

plt.show()

"""## 2.5 Xử lý mất cân bằng trên tập Train"""

print("\nPhân phối lớp trước SMOTE (trên tập train):")
print(y_train.value_counts())

sm = SMOTE(random_state=42)
X_train_res, y_train_res = sm.fit_resample(X_train, y_train)

print("\nPhân phối lớp sau SMOTE (trên tập train):")
print(pd.Series(y_train_res).value_counts())

plt.figure(figsize=(10, 6))
sns.countplot(x=y_train_res, palette='viridis')
plt.title('Phân phối Nghỉ việc sau SMOTE (tập huấn luyện)')
plt.xlabel('Nghỉ việc (0: Không, 1: Có)')
plt.ylabel('Số lượng nhân viên')
counts = pd.Series(y_train_res).value_counts()
for i, count in enumerate(counts):
    plt.text(i, count, str(count), ha='center', va='bottom')
plt.show()

"""## 2.6 Chuẩn hóa dữ liệu"""

scaler = StandardScaler()
X_train_scaled = scaler.fit_transform(X_train_res)
X_test_scaled = scaler.transform(X_test) # Dùng scaler đã fit từ train

"""# **BƯỚC 3: Phân tích dữ liệu trên tập huấn luyện (Chỉnh sửa)**

## 3.1 Thống kê tương quan
"""

# Chuyển X_train_scaled về DataFrame để dễ thao tác và trực quan hơn
X_train_scaled_df = pd.DataFrame(X_train_scaled, columns=processed_columns)

# Thống kê tương quan giữa các biến với biến mục tiêu y_train_res
df_corr = X_train_scaled_df.copy()
df_corr['Attrition'] = y_train_res

# Tính hệ số tương quan Pearson với biến mục tiêu
correlations = df_corr.corr()['Attrition'].sort_values(ascending=False)
print("\nCác biến có tương quan với biến mục tiêu (Attrition) trên tập huấn luyện:")
display(correlations.head(15)) # In 15 biến hàng đầu

"""## 3.2 Biểu đồ tương quan"""

top_features = correlations.abs().sort_values(ascending=False).index[1:11]
plt.figure(figsize=(10,6))
sns.barplot(x=correlations[top_features], y=top_features, palette='viridis')
plt.title("Top 10 biến có tương quan mạnh nhất với 'Nghỉ_Việc' (trên tập huấn luyện)")
plt.xlabel("Hệ số tương quan Pearson")
plt.ylabel("Tên biến")
plt.show()

"""## Giải thích hệ số tương quan
![Hệ số tương quan.png](data:image/png;base64,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

# **BƯỚC 4: Lựa chọn các mô hình để huấn luyện** (dùng chung)
"""

kf = StratifiedKFold(n_splits=10, shuffle=True, random_state=42) # Chia dữ liệu với StratifiedKFold

models = {
    "KNN": (KNeighborsClassifier(), {'n_neighbors': [3, 5, 7]}),
    "DecisionTree": (DecisionTreeClassifier(), {'max_depth': [3, 5, 10]}),
    "RandomForest": (RandomForestClassifier(), {'n_estimators': [50, 100], 'max_depth': [5, 10]}),
    "SVM": (SVC(probability=True), {'C': [0.1, 1, 10], 'kernel': ['linear', 'rbf']}),
    "ANN": (MLPClassifier(max_iter=500), {'hidden_layer_sizes': [(50,), (100,)], 'activation': ['relu', 'tanh']}),

    "LogisticRegression": (
        LogisticRegression(max_iter=1000),
        {'C': [0.1, 1, 10], 'solver': ['liblinear', 'lbfgs']}
    ),
    "GradientBoosting": (
        GradientBoostingClassifier(),
        {'n_estimators': [50, 100], 'learning_rate': [0.05, 0.1], 'max_depth': [3, 5]}
    ),
    "AdaBoost": (
        AdaBoostClassifier(),
        {'n_estimators': [50, 100], 'learning_rate': [0.05, 0.1, 0.5]}
    ),
    "ExtraTrees": (
        ExtraTreesClassifier(),
        {'n_estimators': [50, 100], 'max_depth': [5, 10, None]}
    ),
    "NaiveBayes": (
        GaussianNB(),
        {}  # Không có tham số để tinh chỉnh
    )
}

"""# **BƯỚC 5: Huấn luyện, chọn tham số tối ưu và kiểm thử (Chỉnh sửa)**"""

# Huấn luyện mô hình và tìm ra mô hình tốt nhất
best_models = {}
print("\n--- Bắt đầu quá trình huấn luyện và tìm kiếm mô hình tốt nhất ---")
for name, (model, params) in models.items():
    grid = GridSearchCV(model, params, cv=kf, scoring='recall', n_jobs=-1) # Lựa chọn tham số tốt nhất, mô hình tốt nhất bằng GridSeachCV, ưu tiên lớp 1 (chọn scoring =recall hoặc scoring = f1)
    grid.fit(X_train_scaled, y_train_res) # HUẤN LUYỆN TRÊN TẬP TRAIN ĐÃ XỬ LÝ
    best_models[name] = (grid.best_score_, grid.best_estimator_)

# Tạo, sắp xếp và hiển thị bảng kết quả
metrics_df = pd.DataFrame({
    name: {"Mean CV Accuracy": score}
    for name, (score, model) in best_models.items()
}).T
metrics_df = metrics_df.sort_values(by='Mean CV Accuracy', ascending=False)
print("\nBảng so sánh hiệu quả các mô hình:")
display(metrics_df)

# Lấy ra mô hình có accuracy cao nhất trên tập cross-validation
best_model_name, (best_score, final_model) = max(best_models.items(), key=lambda item: item[1][0])
print(f"\n Mô hình tốt nhất: {best_model_name} with CV recall: {best_score:.4f}")

"""## **BƯỚC 6: Đánh giá hiệu quả của mô hình tốt nhất trên tập TEST**"""

import numpy as np
from sklearn.metrics import precision_recall_fscore_support

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns

from sklearn.metrics import classification_report, confusion_matrix, precision_recall_fscore_support

# --- Dữ liệu thực tế và mô hình của bạn phải được định nghĩa TRƯỚC khi chạy phần này ---
# Ví dụ:
# X_test_scaled: dữ liệu đặc trưng đã được chuẩn hóa của tập test
# y_test: nhãn thực tế của tập test
# final_model: mô hình đã được huấn luyện
#
# Nếu các biến này chưa được định nghĩa, bạn cần định nghĩa chúng tại đây
# hoặc đảm bảo chúng được truyền từ các phần code trước đó của bạn.

# Lấy xác suất dự đoán trên tập test
y_probs = final_model.predict_proba(X_test_scaled)[:, 1]

# Tối ưu ngưỡng phân lớp
thresholds = np.arange(0.1, 0.9, 0.05)
results = []
for thresh in thresholds:
    y_pred_thresh = (y_probs >= thresh).astype(int)
    precision, recall, f1, _ = precision_recall_fscore_support(y_test, y_pred_thresh, average=None, labels=[0,1], zero_division=0)
    results.append({
        'threshold': thresh,
        'precision_0': precision[0],
        'recall_0': recall[0],
        'f1_0': f1[0],
        'precision_1': precision[1],
        'recall_1': recall[1],
        'f1_1': f1[1],
        'recall_diff': abs(recall[0] - recall[1])
    })
df_thresh = pd.DataFrame(results)

print("\nBảng đánh giá theo ngưỡng:")
print(df_thresh[['threshold', 'precision_0', 'recall_0', 'f1_0', 'precision_1', 'recall_1', 'f1_1', 'recall_diff']])

# Vẽ biểu đồ các metric theo threshold
plt.figure(figsize=(10,6))
plt.plot(df_thresh['threshold'], df_thresh['recall_1'], label='Recall lớp 1')
plt.plot(df_thresh['threshold'], df_thresh['precision_1'], label='Precision lớp 1')
plt.plot(df_thresh['threshold'], df_thresh['f1_1'], label='F1-score lớp 1')
plt.plot(df_thresh['threshold'], df_thresh['recall_0'], label='Recall lớp 0', linestyle='--')
plt.plot(df_thresh['threshold'], df_thresh['precision_0'], label='Precision lớp 0', linestyle='--')
plt.plot(df_thresh['threshold'], df_thresh['f1_0'], label='F1-score lớp 0', linestyle='--')
plt.xlabel('Threshold')
plt.ylabel('Score')
plt.title('Đánh giá các metric theo threshold')
plt.legend()
plt.grid(True)
plt.show()

# Tìm ngưỡng có recall lớp 0 và lớp 1 gần nhau nhất (ngưỡng cân bằng mặc định)
best_row = df_thresh.loc[df_thresh['recall_diff'].idxmin()]
best_thresh_default = best_row['threshold'] # Đổi tên biến để tránh nhầm lẫn

print(f"\nNgưỡng phân lớp CÂN BẰNG MẶC ĐỊNH (từ chênh lệch Recall nhỏ nhất): {best_thresh_default:.2f}")
print(f"→ Recall lớp 0: {best_row['recall_0']:.2f} | Recall lớp 1: {best_row['recall_1']:.2f}")

# --- TỰ ĐỘNG GÁN NGƯỠNG THỦ CÔNG LÀ 0.25 ---
final_threshold_for_prediction = 0.1
print(f"\nĐang sử dụng ngưỡng phân lớp đã được gán sẵn: {final_threshold_for_prediction:.2f}")


# Dự đoán với ngưỡng đã chọn (0.25)
y_pred_final = (y_probs >= final_threshold_for_prediction).astype(int)

# In báo cáo phân loại
print(f"\n--- Báo cáo phân loại với ngưỡng {final_threshold_for_prediction:.2f} ---")
print(classification_report(y_test, y_pred_final, target_names=['Lớp 0 (không nghỉ)', 'Lớp 1 (nghỉ việc)'], zero_division=0))

# Vẽ ma trận nhầm lẫn
cm = confusion_matrix(y_test, y_pred_final)
plt.figure(figsize=(7,6))
sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
            xticklabels=['Dự đoán không nghỉ ', 'Dự đoán nghỉ việc'],
            yticklabels=['Thực tế không nghỉ', 'Thực tế nghỉ việc'])
plt.title(f'Ma trận nhầm lẫn (Threshold = {final_threshold_for_prediction:.2f})')
plt.ylabel('Nhãn thực tế')
plt.xlabel('Nhãn dự đoán')
plt.show()

# In tổng quan số lượng mẫu
print(f"\nTổng số mẫu trong tập kiểm tra: {len(y_test)}")
print(f"Số mẫu thực tế Lớp 0 (không nghỉ): {np.sum(y_test == 0)}")
print(f"Số mẫu thực tế Lớp 1 (nghỉ việc): {np.sum(y_test == 1)}")

"""* Mục tiêu của bài toán này là **dự đoán chính xác những nhân viên có khả năng nghỉ việc** dựa trên các đặc trưng về nhân sự, nhằm giúp doanh nghiệp chủ động có các biện pháp giữ chân nhân tài và giảm thiểu rủi ro về nguồn lực.

* Trong thực tế, số lượng nhân viên nghỉ việc thường chiếm tỷ lệ nhỏ so với tổng số nhân viên, dẫn đến vấn đề mất cân bằng dữ liệu.

* Do mục tiêu của bài toán là không bỏ sót bất kỳ nhân viên nào thực sự có nguy cơ nghỉ việc, nhóm lựa chọn tối ưu mô hình theo chỉ số recall của lớp nghỉ việc. Điều này đồng nghĩa với việc mô hình sẽ ưu tiên phát hiện tối đa các trường hợp nghỉ việc, chấp nhận rằng có thể sẽ có một số cảnh báo nhầm (dự đoán nghỉ việc cho nhân viên thực ra không nghỉ). Đây là sự đánh đổi hợp lý trong bối cảnh doanh nghiệp thường quan tâm nhiều hơn đến việc phát hiện sớm các nguy cơ nghỉ việc, thay vì chỉ tập trung vào độ chính xác tổng thể.

* Hệ quả của lựa chọn này là mô hình sẽ có recall lớp nghỉ việc cao, đảm bảo mục tiêu phát hiện đầy đủ các trường hợp quan trọng, nhưng precision có thể giảm, tức là có thể có nhiều cảnh báo nhầm. Tuy nhiên, điều này là phù hợp với thực tiễn quản trị nhân sự, khi việc bỏ sót một trường hợp nghỉ việc có thể gây thiệt hại lớn hơn so với việc kiểm tra lại một số cảnh báo nhầm.

# **BƯỚC 7: Cài đặt**
"""

#Lưu mô hình và Test bằng dữ liệu (Chỉnh sửa)
# Lưu mô hình, scaler và danh sách cột
joblib.dump(final_model, 'best_model.pkl')
joblib.dump(scaler, 'scaler.pkl')
joblib.dump(processed_columns, 'processed_columns.pkl')
print("\n Mô hình, scaler và danh sách cột đã được lưu.")

"""#**Kiểm thử khi đã cài đặt mô hình tốt nhất**

## Dữ liệu tự cho
"""

import joblib
import pandas as pd

def predict_attrition(sample_dict, threshold=0.25):
    """
    Hàm dự đoán khả năng nghỉ việc cho một mẫu dữ liệu mới.
    - sample_dict: một dictionary chứa dữ liệu của nhân viên.
    - threshold: ngưỡng phân lớp dùng để phân biệt nghỉ việc hay không (mặc định 0.25).
    """
    # Tải lại các đối tượng đã lưu
    try:
        model = joblib.load('best_model.pkl')
        scaler = joblib.load('scaler.pkl')
        columns = joblib.load('processed_columns.pkl')
    except FileNotFoundError:
        print("Lỗi: Không tìm thấy file model/scaler/columns. Vui lòng huấn luyện lại mô hình.")
        return

    # Chuyển dictionary thành DataFrame
    new_sample_df = pd.DataFrame([sample_dict])

    # Áp dụng One-Hot Encoding
    new_sample_df = pd.get_dummies(new_sample_df)

    # Căn chỉnh các cột để khớp với dữ liệu lúc train
    new_sample_aligned = new_sample_df.reindex(columns=columns, fill_value=0)

    # Scale mẫu mới
    new_sample_scaled = scaler.transform(new_sample_aligned)

    # Tính xác suất dự đoán lớp 1 (nghỉ việc)
    probability = model.predict_proba(new_sample_scaled)
    prob_class_1 = probability[:, 1]

    # Áp dụng ngưỡng để dự đoán
    prediction = (prob_class_1 >= threshold).astype(int)

    # In kết quả
    result = 'Thôi việc' if prediction[0] == 1 else 'Không thôi việc'
    print(f"\nDỰ ĐOÁN CHO MẪU MỚI:")
    print(f"-> Kết quả: {result}")
    print(f"-> Xác suất Không thôi việc: {probability[0][0]:.2f}")
    print(f"-> Xác suất Thôi việc: {probability[0][1]:.2f}")

# Ví dụ sử dụng hàm dự đoán
new_employee_data = {
    'Tuổi': 35,
    'Đi_Công_Tác': 'Travel_Rarely',
    'Lương_Ngày': 900,
    'Phòng_Ban': 'Sales',
    'Khoảng_Cách_Tới_Nhà': 10,
    'Trình_Độ_Học_Vấn': 3,
    'Ngành_Học': 'Marketing',
    'Mức_Độ_Hài_Lòng_Môi_Trường': 3,
    'Giới_Tính': 'Male',
    'Lương_Giờ': 60,
    'Mức_Độ_Tham_Gia_Công_Việc': 3,
    'Cấp_Bậc_Công_Việc': 2,
    'Chức_Vụ': 'Sales Executive',
    'Mức_Độ_Hài_Lòng_Công_Việc': 4,
    'Tình_Trạng_Hôn_Nhân': 'Married',
    'Lương_Tháng': 5000,
    'Lương_Tháng_Theo_Tỷ_Lệ': 15000,
    'Số_Công_Ty_Đã_Làm': 2,
    'Làm_Thêm': 'No',
    'Phần_Trăm_Tăng_Lương': 15,
    'Đánh_Giá_Hiệu_Suất': 3,
    'Mức_Hài_Lòng_Mối_Quan_Hệ': 3,
    'Cấp_Độ_Tùy_Chọn_Cổ_Phần': 0,
    'Tổng_Số_Năm_Làm_Việc': 10,
    'Số_Lần_Đào_Tạo_Năm_Trước': 2,
    'Cân_Bằng_Cuộc_Sống_Công_Việc': 3,
    'Số_Năm_Làm_Tại_Công_Ty': 5,
    'Số_Năm_Làm_Vị_Trí_Hiện_Tại': 3,
    'Số_Năm_Từ_Lần_Thăng_Chức_Gần_Nhất': 1,
    'Số_Năm_Với_Quản_Lý_Hiện_Tại': 2
}

predict_attrition(new_employee_data)

"""## Dữ liệu từ dataset"""

print("\n" + "="*40)
print(">>> KIỂM TRA VỚI DÒNG THỨ NHẤT <<<")

# Lấy dòng thứ 2 (index 1 vì index bắt đầu từ 0)
second_row_to_test = df.iloc[0]

# Chuyển Series thành dictionary
second_row_dict = second_row_to_test.to_dict()

# Lấy giá trị thực tế và xóa cột nhãn
actual_result_text = second_row_dict.pop('Nghỉ_Việc')
print(f"\nGiá trị thực tế: '{actual_result_text}' ({'Thôi việc' if actual_result_text == 'Yes' or actual_result_text == 1 else 'Không thôi việc'})")

# Gọi hàm dự đoán với dữ liệu dòng thứ 2
predict_attrition(second_row_dict)

print("="*40)
# === BƯỚC 4: Huấn luyện mô hình với Pipeline và GridSearchCV ===
from imblearn.pipeline import Pipeline as ImbPipeline
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import classification_report, confusion_matrix
from sklearn.model_selection import GridSearchCV, StratifiedKFold

# Tạo pipeline: scaler -> SMOTE -> classifier
pipeline = ImbPipeline([
    ('scaler', StandardScaler()),
    ('smote', SMOTE(random_state=42)),
    ('clf', RandomForestClassifier(random_state=42))
])

# Tham số để GridSearch (bạn có thể mở rộng thêm)
param_grid = {
    'clf__n_estimators': [100, 200],
    'clf__max_depth': [None, 10, 20],
}

cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)
grid = GridSearchCV(pipeline, param_grid, cv=cv, scoring='f1', n_jobs=-1)
grid.fit(X_train, y_train)

print("\nBest params:", grid.best_params_)
print("Best CV score:", grid.best_score_)

# Đánh giá trên tập test
print("\n=== Đánh giá trên tập test (hold-out) ===")
y_pred = grid.predict(X_test)
print(confusion_matrix(y_test, y_pred))
print(classification_report(y_test, y_pred))